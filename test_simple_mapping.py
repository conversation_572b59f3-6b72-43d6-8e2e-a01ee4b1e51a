#!/usr/bin/env python3
"""
Simple test to verify the hierarchy mapping works correctly.
"""

# Sample user data
sample_user_data = {
    "id": "user-123",
    "email": "<EMAIL>",
    "userProfile": {
        "name": "Admin User",
        "user": {
            "Role": {
                "roles": "Admin",
                "permissions": [
                    "GnXj6Mmj.readWrite",
                    "yPa9RIAl.read",
                    "7b9wfMEj.readWrite"
                ],
                "baseHierarchy": "GnXj6Mmj"
            }
        }
    }
}

# Expected output after mapping
expected_accessible_hierarchies = [
    {
        "id": "GnXj6Mmj",
        "name": "Schindler India",
        "access_level": "readWrite",
        "permission": "GnXj6Mmj.readWrite"
    },
    {
        "id": "yPa9RIAl",
        "name": "INFRA TRD",
        "access_level": "read",
        "permission": "yPa9RIAl.read"
    },
    {
        "id": "7b9wfMEj",
        "name": "NR1",
        "access_level": "readWrite",
        "permission": "7b9wfMEj.readWrite"
    }
]

def test_simple_mapping():
    print("🧪 Testing Simple Hierarchy Mapping")
    print("=" * 40)
    
    # Extract data
    role_obj = sample_user_data["userProfile"]["user"]["Role"]
    permissions = role_obj.get("permissions", [])
    base_hierarchy = role_obj.get("baseHierarchy")
    
    print(f"\n📋 Input Data:")
    print(f"  Permissions: {permissions}")
    print(f"  Base Hierarchy: {base_hierarchy}")
    
    print(f"\n🎯 Expected Output:")
    print(f"  Base Hierarchy Title: Schindler India")
    print(f"  Accessible Hierarchies:")
    for hierarchy in expected_accessible_hierarchies:
        print(f"    • {hierarchy['name']} ({hierarchy['id']}): {hierarchy['access_level']}")
    
    print(f"\n✅ This is what the SafetyConnectUser class will provide:")
    print(f"  user.permissions = {permissions}")
    print(f"  user.accessible_hierarchies = [decoded permissions with names]")
    print(f"  user.base_hierarchy = '{base_hierarchy}'")
    print(f"  user.base_hierarchy_title = 'Schindler India'")
    
    print(f"\n🌐 API Response will include:")
    api_response = {
        "user_id": "user-123",
        "email": "<EMAIL>",
        "name": "Admin User",
        "role": "Admin",
        "permissions": permissions,
        "accessible_hierarchies": expected_accessible_hierarchies,
        "base_hierarchy": base_hierarchy,
        "base_hierarchy_title": "Schindler India"
    }
    
    print(f"  Keys: {list(api_response.keys())}")
    print(f"  Permissions count: {len(api_response['permissions'])}")
    print(f"  Accessible hierarchies count: {len(api_response['accessible_hierarchies'])}")

if __name__ == "__main__":
    test_simple_mapping()
