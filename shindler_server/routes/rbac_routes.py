from fastapi import APIRouter, Depends
from typing import List

from services.auth_service import SafetyConnectU<PERSON>, get_current_user

router = APIRouter(prefix="/api/v1/rbac", tags=["RBAC & Region Access"])


# ----- User Profile & Region Access Info -----
@router.get("/profile")
def get_user_profile(current_user: SafetyConnectUser = Depends(get_current_user)):
    """Get current user profile from SafetyConnect API with new naming conventions"""
    return current_user.to_dict()


@router.get("/my-region-access")
def get_my_region_access(current_user: SafetyConnectUser = Depends(get_current_user)):
    """Get current user's region access permissions"""
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "name": current_user.name,
        "role": current_user.role,
        "region_access": current_user.permissions,
        "permissions_count": len(current_user.permissions),
        "regions": current_user.accessible_hierarchies,
        "base_hierarchy": current_user.base_hierarchy,
        "customer_region": current_user.base_hierarchy_title,
        "company": current_user.company_name,
        "region": current_user.region
    }


@router.get("/my-regions")
def get_my_regions(current_user: SafetyConnectUser = Depends(get_current_user)):
    """Get current user's accessible regions (simplified)"""
    return {
        "user_id": current_user.id,
        "regions": current_user.accessible_hierarchies,
        "regions_count": len(current_user.accessible_hierarchies),
        "base_hierarchy": current_user.base_hierarchy,
        "customer_region": current_user.base_hierarchy_title
    }


# ----- Region Access Checking -----
@router.post("/check-region-access")
def check_region_access(
    region_access_to_check: List[str],
    current_user: SafetyConnectUser = Depends(get_current_user)
):
    """Check if current user has specific region access permissions"""
    missing_region_access = [
        perm for perm in region_access_to_check
        if not current_user.has_permission(perm)
    ]

    return {
        "user_id": current_user.id,
        "region_access_checked": region_access_to_check,
        "has_region_access": len(missing_region_access) == 0,
        "missing_region_access": missing_region_access,
        "user_regions": current_user.accessible_hierarchies
    }


@router.get("/my-role-info")
def get_my_role_info(current_user: SafetyConnectUser = Depends(get_current_user)):
    """Get current user's role and region access information"""
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "name": current_user.name,
        "role": current_user.role,
        "region_access": current_user.permissions,
        "permissions_count": len(current_user.permissions),
        "regions": current_user.accessible_hierarchies,
        "base_hierarchy": current_user.base_hierarchy,
        "customer_region": current_user.base_hierarchy_title,
        "company": current_user.company_name,
        "region": current_user.region
    }




