"""
Example protected routes using SafetyConnect permissions
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import Optional

from services.auth_service import (
    SafetyConnectUser, get_current_user, require_permissions, 
    require_any_permissions, require_roles, check_regional_access
)

router = APIRouter(prefix="/api/v1/protected", tags=["Protected Routes Examples"])


# Example 1: Check specific permission
@router.get("/dashboard/safety")
def get_safety_dashboard(
    current_user: SafetyConnectUser = Depends(require_permissions(["GnXj6Mmj.readWrite"]))
):
    """
    Safety dashboard - requires specific permission from your system
    """
    return {
        "message": "Safety dashboard data",
        "user": current_user.name,
        "role": current_user.role,
        "company": current_user.company_name,
        "region_access_count": len(current_user.permissions)
    }


# Example 2: Check multiple permissions (ALL required)
@router.post("/reports/create")
def create_report(
    current_user: SafetyConnectUser = Depends(require_permissions([
        "GnXj6Mmj.readWrite", "yPa9RIAl.readWrite"
    ]))
):
    """
    Create report - requires multiple specific permissions
    """
    return {
        "message": "Report creation authorized",
        "user": current_user.name,
        "role": current_user.role,
        "base_hierarchy": current_user.base_hierarchy
    }


# Example 3: Check any permission (ONE required)
@router.get("/data/view")
def view_data(
    data_type: str = Query(..., description="Type of data to view"),
    current_user: SafetyConnectUser = Depends(require_any_permissions([
        "GnXj6Mmj.read", "GnXj6Mmj.readWrite", "yPa9RIAl.read", "yPa9RIAl.readWrite"
    ]))
):
    """
    View data - requires ANY of the specified read permissions
    """
    return {
        "message": f"Data view authorized for {data_type}",
        "user": current_user.name,
        "region_access": current_user.permissions[:5]  # Show first 5 region access permissions
    }


# Example 4: Role-based access
@router.get("/admin/settings")
def admin_settings(
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin"]))
):
    """
    Admin settings - requires accountAdmin role specifically
    """
    return {
        "message": "Admin settings access granted",
        "admin_user": current_user.name,
        "role": current_user.role,
        "subscription_status": current_user.subscription_status
    }


# Example 5: Custom permission check in route
@router.get("/custom-check")
def custom_permission_check(
    action: str = Query(..., description="Action to perform"),
    current_user: SafetyConnectUser = Depends(get_current_user)
):
    """
    Custom permission checking within the route
    """
    
    # Define permission requirements based on action
    permission_map = {
        "read": ["GnXj6Mmj.read", "GnXj6Mmj.readWrite"],
        "write": ["GnXj6Mmj.readWrite"],
        "delete": ["GnXj6Mmj.readWrite", "yPa9RIAl.readWrite"]
    }
    
    required_perms = permission_map.get(action, [])
    if not required_perms:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unknown action: {action}"
        )
    
    # Check if user has any of the required permissions
    if not current_user.has_any_permission(required_perms):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Action '{action}' requires one of: {', '.join(required_perms)}"
        )
    
    return {
        "message": f"Action '{action}' authorized",
        "user": current_user.name,
        "required_region_access": required_perms,
        "user_has_region_access": [p for p in required_perms if current_user.has_permission(p)]
    }


# Example 6: Regional access control
@router.get("/regional/data")
def get_regional_data(
    region: Optional[str] = Query(None, description="Specific region to access"),
    current_user: SafetyConnectUser = Depends(require_permissions(["GnXj6Mmj.read"]))
):
    """
    Regional data with access control
    """
    
    # Check regional access
    if not check_regional_access(current_user, region):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Access denied to region: {region}"
        )
    
    # Use user's region if none specified
    effective_region = region or current_user.region
    
    return {
        "message": "Regional data access granted",
        "region": effective_region,
        "user_region": current_user.region,
        "user_role": current_user.role,
        "company": current_user.company_name
    }


# Example 7: Permission info endpoint
@router.get("/my-access")
def get_my_access_info(current_user: SafetyConnectUser = Depends(get_current_user)):
    """
    Get detailed information about current user's access
    """
    
    # Categorize permissions
    read_permissions = [p for p in current_user.permissions if p.endswith('.read')]
    write_permissions = [p for p in current_user.permissions if p.endswith('.readWrite')]
    
    return {
        "user_info": {
            "id": current_user.id,
            "name": current_user.name,
            "email": current_user.email,
            "role": current_user.role,
            "company": current_user.company_name,
            "region": current_user.region
        },
        "access_summary": {
            "total_region_access": len(current_user.permissions),
            "read_region_access": len(read_permissions),
            "write_region_access": len(write_permissions),
            "base_hierarchy": current_user.base_hierarchy,
            "customer_region": current_user.base_hierarchy_title
        },
        "region_access": {
            "read_only": read_permissions,
            "read_write": write_permissions
        }
    }


# Example 8: Public endpoint (no authentication)
@router.get("/public/info")
def public_info():
    """
    Public endpoint - no authentication required
    """
    return {
        "message": "This is a public endpoint",
        "info": "No authentication required",
        "timestamp": "2025-08-20"
    }
