from __future__ import annotations
from datetime import datetime
import os
from typing import Optional, Dict, Any, List
import json
import requests
from functools import lru_cache

from jose import jwt, jwk
from jose.utils import base64url_decode
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union
from fastapi import Depends, HTTPException, status


# AWS Cognito configuration
COGNITO_REGION = os.getenv("COGNITO_REGION", "ap-south-1")
COGNITO_USER_POOL_ID = os.getenv("COGNITO_USER_POOL_ID", "ap-south-1_Efg4JugXN")
COGNITO_CLIENT_ID = os.getenv("COGNITO_CLIENT_ID")
COGNITO_ISSUER = f"https://cognito-idp.{COGNITO_REGION}.amazonaws.com/{COGNITO_USER_POOL_ID}"


# Role-based permissions mapping
ROLE_PERMISSIONS = {
    "super_admin": [
        "user:create", "user:read", "user:update", "user:delete",
        "role:create", "role:read", "role:update", "role:delete",
        "permission:create", "permission:read", "permission:update", "permission:delete",
        "dashboard:view", "dashboard:create", "dashboard:update", "dashboard:delete", "dashboard:export",
        "analytics:view", "analytics:export", "analytics:advanced",
        "data:all", "region:all", "file:upload", "file:download", "file:delete",
        "admin:all", "system:seed", "system:backup", "system:maintenance"
    ],
    "safety_head": [
        "dashboard:view", "dashboard:create", "dashboard:update", "dashboard:export",
        "analytics:view", "analytics:export", "analytics:advanced",
        "data:all", "region:all", "file:upload", "file:download",
        "user:read", "role:read", "permission:read"
    ],
    "cxo": [
        "dashboard:view", "dashboard:export",
        "analytics:view", "analytics:export", "analytics:advanced",
        "data:all", "region:all", "file:download"
    ],
    "safety_manager": [
        "dashboard:view", "dashboard:export",
        "analytics:view", "analytics:export",
        "data:all", "file:upload", "file:download"
    ],
    "safety_analyst": [
        "dashboard:view", "analytics:view", "data:all", "file:download"
    ],
    "data_entry": [
        "file:upload", "file:download", "dashboard:view"
    ]
}


@lru_cache(maxsize=10)
def get_cognito_public_keys():
    """Get Cognito public keys for JWT verification (cached)"""
    try:
        jwks_url = f"{COGNITO_ISSUER}/.well-known/jwks.json"
        print(f"🔍 Fetching JWKS from: {jwks_url}")
        response = requests.get(jwks_url, timeout=10)
        print(f"🔍 JWKS Response Status: {response.status_code}")
        jwks_data = response.json()
        print(f"🔍 JWKS Keys Count: {len(jwks_data.get('keys', []))}")
        for i, key in enumerate(jwks_data.get('keys', [])):
            print(f"🔍 Key {i+1}: kid={key.get('kid')}, alg={key.get('alg')}, use={key.get('use')}")
        response.raise_for_status()
        return jwks_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch Cognito public keys: {str(e)}"
        )


def verify_cognito_token(token: str) -> Dict[str, Any]:
    """Verify AWS Cognito JWT token using jose library (same as your working notebook)"""
    try:
        # Get the token header to find the key ID
        headers = jwt.get_unverified_headers(token)
        print(f"🔍 JWT Header: {headers}")
        kid = headers.get("kid")
        print(f"🔍 JWT Key ID (kid): {kid}")

        if not kid:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token missing key ID"
            )

        # Get public keys from Cognito
        jwks_data = get_cognito_public_keys()
        jwks_keys = jwks_data.get("keys", [])

        # Find the matching key
        key = next((k for k in jwks_keys if k["kid"] == kid), None)
        if key is None:
            available_kids = [k.get("kid") for k in jwks_keys]
            print(f"🔍 Available key IDs: {available_kids}")
            print(f"🔍 Looking for key ID: {kid}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Public key not found. Token kid: {kid}, Available kids: {available_kids}"
            )

        print(f"🔍 Found matching key: {kid}")

        # Construct the public key
        public_key = jwk.construct(key)

        # Verify signature manually (like in your notebook)
        message, encoded_signature = token.rsplit('.', 1)
        decoded_signature = base64url_decode(encoded_signature.encode("utf-8"))

        if not public_key.verify(message.encode("utf-8"), decoded_signature):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Signature verification failed"
            )

        # Token signature is valid, decode and return claims
        claims = jwt.decode(
            token,
            public_key,
            algorithms=["RS256"],
            audience=None,  # Optional: You can validate audience if needed
            issuer=COGNITO_ISSUER
        )

        print(f"🔍 Token verified successfully for user: {claims.get('email', claims.get('username', 'unknown'))}")
        print(claims)
        return claims

    except HTTPException:
        # Re-raise HTTPExceptions as-is
        raise
    except Exception as e:
        print(f"🔍 Token verification error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token verification failed: {str(e)}"
        )


class SafetyConnectUser:
    """User class with data from SafetyConnect API"""

    def __init__(self, user_data: Dict[str, Any]):
        # Basic user info
        self.id = user_data.get("id")
        self.email = user_data.get("email")
        self.contact = user_data.get("contact", {})
        self.customer_id = user_data.get("customerId")

        # User profile info
        user_profile = user_data.get("userProfile", {})
        self.name = user_profile.get("name")
        self.designation = user_profile.get("designation")
        self.department = user_profile.get("department")
        self.azure_id = user_profile.get("azureId")

        # Role and permissions from nested structure
        user_obj = user_profile.get("user", {})
        role_obj = user_obj.get("Role", {})
        self.role = role_obj.get("roles")
        print(f"🔍 User role extracted: '{self.role}' from API response")

        # Get hierarchy mapping for decoding IDs to names
        hierarchy_mapping = fetch_hierarchy_data()

        # Get permissions and decode them with hierarchy names
        raw_permissions = role_obj.get("permissions", [])
        self.permissions = raw_permissions
        self.accessible_hierarchies = map_region_access_to_titles(raw_permissions, hierarchy_mapping)

        # Get base hierarchy and decode to name
        self.base_hierarchy = role_obj.get("baseHierarchy")
        self.base_hierarchy_title = hierarchy_mapping.get(self.base_hierarchy) if self.base_hierarchy else None

        # Customer info
        customer = user_data.get("customer", {})
        self.company_name = customer.get("companyName")
        self.region = customer.get("region")
        self.country = customer.get("country")
        self.state = customer.get("state")
        self.time_zone = customer.get("timeZone")
        self.industry_type = customer.get("industryType")

        # Subscription info
        subscription = user_data.get("subscription", {})
        self.subscription_status = subscription.get("status")
        self.subscription_id = subscription.get("id")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "email": self.email,
            "name": self.name,
            "role": self.role,
            "permissions": self.permissions,
            "accessible_hierarchies": self.accessible_hierarchies,
            "department": self.department,
            "designation": self.designation,
            "company_name": self.company_name,
            "region": self.region,
            "country": self.country,
            "base_hierarchy": self.base_hierarchy,
            "base_hierarchy_title": self.base_hierarchy_title,
            "subscription_status": self.subscription_status,
            "contact": self.contact
        }

    def has_permission(self, permission_code: str) -> bool:
        """Check if user has a specific permission"""
        return permission_code in self.permissions

    def has_any_permission(self, permission_codes: List[str]) -> bool:
        """Check if user has any of the specified permissions"""
        return any(perm in self.permissions for perm in permission_codes)

    def has_all_permissions(self, permission_codes: List[str]) -> bool:
        """Check if user has all of the specified permissions"""
        return all(perm in self.permissions for perm in permission_codes)

http_bearer = HTTPBearer(auto_error=True)

# SafetyConnect API configuration
SAFETY_CONNECT_API_BASE = os.getenv("SAFETY_CONNECT_API_BASE", "https://devpsapi.safetyconnect.io")


def build_hierarchy_mapping(tree_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Build a mapping from hierarchy IDs to their titles from the tree structure.

    Args:
        tree_data: The tree structure containing hierarchical data

    Returns:
        Dict mapping ID to title (e.g., {"GnXj6Mmj": "Schindler India"})
    """
    mapping = {}

    def traverse_tree(node: Dict[str, Any]):
        """Recursively traverse the tree and build the mapping"""
        if "id" in node and "title" in node:
            mapping[node["id"]] = node["title"]

        # Traverse children if they exist
        if "children" in node and isinstance(node["children"], list):
            for child in node["children"]:
                traverse_tree(child)

    # Start traversal from the root tree
    if "tree" in tree_data:
        traverse_tree(tree_data["tree"])

    return mapping


def get_static_hierarchy_mapping() -> Dict[str, str]:
    """
    Static hierarchy mapping as fallback when API is not available.
    This should be updated when the hierarchy structure changes.

    Returns:
        Dict mapping hierarchy IDs to titles
    """
    return {
        "GnXj6Mmj": "Schindler India",
        "yPa9RIAl": "INFRA TRD",
        "7b9wfMEj": "NR1",
        "3HY8D8WA": "South Delhi",
        "toVf3ibB": "North Delhi",
        "1dE1YJk8": "RoN",
        "60CPPhdt": "NR2",
        "445iX2UQ": "Kolkata",
        "287qoJrD": "JBL",
        "aIgmaapY": "Noida",
        "J9AqIFCx": "ONE",
        "qdp4jRc6": "Gurgaon",
        "Ue80qrPm": "SR1",
        "tKB1TmJq": "Tamil Nadu",
        "BAPz43KJ": "Bangalore 1",
        "TRtwJg5Y": "Cochin",
        "W2EZymdu": "Bangalore 2",
        "T9Z3dVYl": "Mangalore",
        "Wq7oetmT": "Andhra Pradesh",
        "W7OPcwsL": "SR2",
        "fe4JLBZ2": "Hyderabad 1",
        "BeHVGEcv": "Hyderabad 2",
        "oS6nnTJo": "Warangal",
        "j95fxFOX": "WR1",
        "3vAg6CkG": "Thane and KDMC",
        "LKFNZixa": "Mumbai 1",
        "35QOlJYt": "Mumbai 2",
        "Hoe1wdmN": "WR2",
        "0DghxIlw": "Pune 1",
        "iMb8TqjI": "Pune 2",
        "QzOXIF4U": "Chhattisgarh and Nagpur",
        "Mumymmvf": "Surat",
        "tWQPxwAx": "Rajasthan",
        "bfV4s30q": "Goa",
        "HFfZErG5": "Ahmedabad and MP"
    }


@lru_cache(maxsize=10)
def fetch_hierarchy_data() -> Dict[str, str]:
    """
    Fetch hierarchy data from SafetyConnect API and build ID to title mapping.
    This function is cached to avoid repeated API calls.

    Returns:
        Dict mapping hierarchy IDs to titles
    """
    try:
        # You may need to adjust this endpoint based on your API
        headers = {"Authorization": f"Bearer {os.getenv('HIERARCHY_API_TOKEN', '')}"}
        response = requests.get(
            f"{SAFETY_CONNECT_API_BASE}/hierarchy",
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        hierarchy_data = response.json()
        return build_hierarchy_mapping(hierarchy_data)

    except requests.RequestException as e:
        print(f"🔍 Failed to fetch hierarchy data from API: {str(e)}")
        # Fallback to static mapping if API call fails
        return get_static_hierarchy_mapping()


def map_region_access_to_titles(region_access_keys: List[str], hierarchy_mapping: Dict[str, str]) -> List[Dict[str, str]]:
    """
    Map region access keys to their corresponding region titles.

    Args:
        region_access_keys: List of region access strings (e.g., ["GnXj6Mmj.readWrite", "yPa9RIAl.read"])
        hierarchy_mapping: Dict mapping region IDs to region names

    Returns:
        List of dicts with region access details including region names
    """
    mapped_regions = []

    for access_key in region_access_keys:
        if "." in access_key:
            region_id, access_level = access_key.split(".", 1)
            region_name = hierarchy_mapping.get(region_id, f"Unknown Region ({region_id})")

            mapped_regions.append({
                "id": region_id,
                "name": region_name,
                "access_level": access_level,
                "permission": access_key
            })
        else:
            # Handle access keys without region ID (shouldn't happen in normal cases)
            mapped_regions.append({
                "id": None,
                "name": access_key,
                "access_level": None,
                "permission": access_key
            })

    return mapped_regions


def get_base_region_name(base_region_id: str, hierarchy_mapping: Dict[str, str]) -> str:
    """
    Get the region name for a base region ID.

    Args:
        base_region_id: The base region ID (baseHierarchy)
        hierarchy_mapping: Dict mapping region IDs to region names

    Returns:
        The region name corresponding to the base region ID
    """
    return hierarchy_mapping.get(base_region_id, f"Unknown Region ({base_region_id})")


@lru_cache(maxsize=100)
def fetch_user_data_from_api(token: str) -> Dict[str, Any]:
    """Fetch user data from SafetyConnect API using the access token"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{SAFETY_CONNECT_API_BASE}/me", headers=headers, timeout=10)
        response.raise_for_status()

        user_data = response.json()
        print(f"🔍 Fetched user data for: {user_data.get('token2', {}).get('email', 'unknown')}")

        # Extract the main user data from the nested structure
        return user_data.get("token2", {})

    except requests.RequestException as e:
        print(f"🔍 Failed to fetch user data from API: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Failed to fetch user data: {str(e)}"
        )


def get_current_user(
    creds: HTTPAuthorizationCredentials = Depends(http_bearer)
) -> SafetyConnectUser:
    """Get current authenticated user from Cognito JWT token and SafetyConnect API"""
    token = creds.credentials

    # First verify the Cognito JWT token
    jwt_payload = verify_cognito_token(token)
    print(f"🔍 JWT verified for user: {jwt_payload.get('email', 'unknown')}")

    # Then fetch detailed user data from SafetyConnect API
    user_data = fetch_user_data_from_api(token)

    # Create and return SafetyConnectUser with the API data
    return SafetyConnectUser(user_data)


def require_permissions(required_permissions: List[str]):
    """
    Dependency factory for requiring multiple permissions.
    User must have ALL specified permissions.

    Usage:
    @router.get("/users")
    def list_users(user: SafetyConnectUser = Depends(require_permissions(["user:read", "admin:access"]))):
        ...
    """
    def permission_dependency(current_user: SafetyConnectUser = Depends(get_current_user)) -> SafetyConnectUser:
        if not current_user.has_all_permissions(required_permissions):
            missing_permissions = [perm for perm in required_permissions if not current_user.has_permission(perm)]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        return current_user
    return permission_dependency


def require_any_permissions(permission_options: List[str]):
    """
    Dependency factory for requiring ANY of the specified permissions.
    User needs at least one of the permissions.

    Usage:
    @router.get("/data")
    def get_data(user: SafetyConnectUser = Depends(require_any_permissions(["data:read", "admin:all"]))):
        ...
    """
    def permission_dependency(current_user: SafetyConnectUser = Depends(get_current_user)) -> SafetyConnectUser:
        if not current_user.has_any_permission(permission_options):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of these permissions required: {', '.join(permission_options)}"
            )
        return current_user
    return permission_dependency



def require_roles(required_roles: Union[List[str], set[str]]):
    """
    Dependency factory for requiring one of multiple roles.

    Usage:
    @router.get("/admin")
    def admin_endpoint(user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "superAdmin"]))):
        ...
    """
    def role_dependency(current_user: SafetyConnectUser = Depends(get_current_user)) -> SafetyConnectUser:
        print(f"🔍 Role check - User role: '{current_user.role}', Required roles: {required_roles}")
        if current_user.role not in required_roles:
            print(f"🔍 Role mismatch - User role '{current_user.role}' not in {required_roles}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of roles {required_roles} required. Current role: '{current_user.role}'"
            )
        print(f"🔍 Role check passed for user: {current_user.email}")
        return current_user
    return role_dependency


def check_regional_access(user: SafetyConnectUser, requested_region: Optional[str] = None) -> bool:
    """Check if user has access to requested region"""
    # Admin roles have access to all regions
    if user.role in ["accountAdmin", "superAdmin"]:
        return True

    # Regional managers can only access their own region
    if requested_region and user.region:
        return requested_region == user.region

    # Default: allow access
    return True

