#!/usr/bin/env python3
"""
Test script to verify SafetyConnectUser class works with accessible_hierarchies.
"""

import sys
import os
from typing import Dict, List, Any

# Mock the required functions for testing
def get_static_hierarchy_mapping() -> Dict[str, str]:
    return {
        "GnXj6Mmj": "Schindler India",
        "yPa9RIAl": "INFRA TRD",
        "7b9wfMEj": "NR1",
        "3HY8D8WA": "South Delhi",
        "toVf3ibB": "North Delhi"
    }

def map_region_access_to_titles(region_access_keys: List[str], hierarchy_mapping: Dict[str, str]) -> List[Dict[str, str]]:
    unique_regions = {}
    for access_key in region_access_keys:
        if "." in access_key:
            region_id, _ = access_key.split(".", 1)
            region_name = hierarchy_mapping.get(region_id, f"Unknown Region ({region_id})")
            if region_id not in unique_regions:
                unique_regions[region_id] = {
                    "id": region_id,
                    "name": region_name
                }
    return list(unique_regions.values())

def get_base_region_name(base_region_id: str, hierarchy_mapping: Dict[str, str]) -> str:
    return hierarchy_mapping.get(base_region_id, f"Unknown Region ({base_region_id})")

def fetch_hierarchy_data() -> Dict[str, str]:
    return get_static_hierarchy_mapping()

# Mock SafetyConnectUser class with the key parts
class MockSafetyConnectUser:
    def __init__(self, user_data: Dict[str, Any]):
        # Basic user info
        self.id = user_data.get("id")
        self.email = user_data.get("email")
        self.name = user_data.get("userProfile", {}).get("name")
        
        # Role and permissions from nested structure
        user_obj = user_data.get("userProfile", {}).get("user", {})
        role_obj = user_obj.get("Role", {})
        self.role = role_obj.get("roles")
        
        # Get hierarchy mapping for title resolution
        hierarchy_mapping = fetch_hierarchy_data()
        
        # Get permissions and map them to titles
        raw_permissions = role_obj.get("permissions", [])
        self.permissions = raw_permissions
        self.accessible_hierarchies = map_region_access_to_titles(raw_permissions, hierarchy_mapping)
        
        # Get base hierarchy and map to title
        self.base_hierarchy = role_obj.get("baseHierarchy")
        self.base_hierarchy_title = get_base_region_name(
            self.base_hierarchy, hierarchy_mapping
        ) if self.base_hierarchy else None

    def get_permissions_for_hierarchy(self, hierarchy_id: str) -> List[Dict[str, str]]:
        """Get all permissions for a specific hierarchy ID"""
        return [
            hierarchy for hierarchy in self.accessible_hierarchies
            if hierarchy.get("id") == hierarchy_id
        ]

    def has_hierarchy_access(self, hierarchy_id: str, access_level: str = None) -> bool:
        """Check if user has access to a specific hierarchy"""
        for hierarchy in self.accessible_hierarchies:
            if hierarchy.get("id") == hierarchy_id:
                # With simplified structure, we only check if region exists
                # Access level checking would need to be done against raw permissions
                return True
        return False

def test_user_class():
    """Test the SafetyConnectUser class functionality"""
    
    # Sample user data
    sample_user_data = {
        "id": "user-123",
        "email": "<EMAIL>",
        "userProfile": {
            "name": "Admin User",
            "user": {
                "Role": {
                    "roles": "Admin",
                    "permissions": [
                        "GnXj6Mmj.readWrite",
                        "yPa9RIAl.read",
                        "yPa9RIAl.readWrite",
                        "7b9wfMEj.read",
                        "3HY8D8WA.readWrite"
                    ],
                    "baseHierarchy": "GnXj6Mmj"
                }
            }
        }
    }
    
    print("🧪 Testing SafetyConnectUser Class")
    print("=" * 40)
    
    # Create user instance
    user = MockSafetyConnectUser(sample_user_data)
    
    print(f"\n👤 User Info:")
    print(f"  ID: {user.id}")
    print(f"  Email: {user.email}")
    print(f"  Name: {user.name}")
    print(f"  Role: {user.role}")
    
    print(f"\n🔑 Raw Permissions:")
    for i, perm in enumerate(user.permissions):
        print(f"  {i+1}. {perm}")
    
    print(f"\n🏢 Accessible Hierarchies:")
    for i, hierarchy in enumerate(user.accessible_hierarchies):
        print(f"  {i+1}. {hierarchy['name']} ({hierarchy['id']})")
    
    print(f"\n🎯 Base Hierarchy:")
    print(f"  ID: {user.base_hierarchy}")
    print(f"  Title: {user.base_hierarchy_title}")
    
    print(f"\n🔍 Testing Methods:")
    
    # Test hierarchy access
    test_cases = [
        ("GnXj6Mmj", None, "Should have access to Schindler India"),
        ("GnXj6Mmj", "readWrite", "Should have readWrite access to Schindler India"),
        ("yPa9RIAl", "read", "Should have read access to INFRA TRD"),
        ("yPa9RIAl", "readWrite", "Should have readWrite access to INFRA TRD"),
        ("unknown", None, "Should NOT have access to unknown region")
    ]
    
    for hierarchy_id, access_level, description in test_cases:
        has_access = user.has_hierarchy_access(hierarchy_id, access_level)
        status = "✅" if has_access else "❌"
        access_str = f" ({access_level})" if access_level else ""
        print(f"  {status} {hierarchy_id}{access_str}: {description}")
    
    # Test get permissions for hierarchy
    print(f"\n📋 Permissions for INFRA TRD (yPa9RIAl):")
    infra_permissions = user.get_permissions_for_hierarchy("yPa9RIAl")
    for perm in infra_permissions:
        print(f"  • {perm['name']} ({perm['id']})")
    
    print(f"\n✅ All tests completed successfully!")
    
    # Show what the API response would look like
    print(f"\n🌐 Sample API Response Structure:")
    api_response = {
        "user_id": user.id,
        "email": user.email,
        "name": user.name,
        "role": user.role,
        "permissions": user.permissions,
        "accessible_hierarchies": user.accessible_hierarchies,
        "base_hierarchy": user.base_hierarchy,
        "base_hierarchy_title": user.base_hierarchy_title
    }
    
    print(f"  Keys in response: {list(api_response.keys())}")
    print(f"  Accessible hierarchies count: {len(api_response['accessible_hierarchies'])}")
    print(f"  Base hierarchy: {api_response['base_hierarchy']} -> {api_response['base_hierarchy_title']}")

if __name__ == "__main__":
    test_user_class()
